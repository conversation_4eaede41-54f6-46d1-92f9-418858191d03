import secrets
from datetime import timed<PERSON><PERSON>
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from ..database import get_async_db
from ..models import User, Task, china_now
from ..schemas import TaskCreate, TaskUpdate, TaskResponse
from ..auth import get_current_active_user
from ..email_verification_required import require_email_verification
from ..task_scheduler import check_and_update_task_status_async, update_expired_tasks
from ..email_hooks import email_hooks

router = APIRouter(prefix="/api/tasks", tags=["任务管理"])


def generate_share_token() -> str:
    """生成分享令牌"""
    return secrets.token_urlsafe(32)


@router.get("/")
async def get_tasks(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取任务列表（管理员可查看所有任务，普通用户只能查看自己的任务）"""
    # 构建查询条件，使用关联查询获取创建者信息
    stmt = select(Task).join(User, Task.creator_id == User.id)

    # 如果不是管理员，只能查看自己创建的任务
    if current_user.role != "admin":
        stmt = stmt.where(Task.creator_id == current_user.id)

    # 获取总数 - 修复笛卡尔积问题：直接计算总数而不使用子查询
    total_stmt = select(func.count(Task.id)).join(User, Task.creator_id == User.id)
    if current_user.role != "admin":
        total_stmt = total_stmt.where(Task.creator_id == current_user.id)
    total_result = await db.execute(total_stmt)
    total = total_result.scalar()

    # 获取分页数据，按创建时间倒序排列（最新的在前面）
    tasks_stmt = stmt.order_by(Task.created_at.desc()).offset(skip).limit(limit)
    tasks_result = await db.execute(tasks_stmt)
    tasks = tasks_result.scalars().all()

    # 检查并更新过期任务状态
    for task in tasks:
        await check_and_update_task_status_async(task.id, db)

    # 重新查询以获取最新状态，并添加创建者用户名
    if current_user.role != "admin":
        refresh_stmt = select(Task).join(User, Task.creator_id == User.id).where(Task.creator_id == current_user.id).order_by(Task.created_at.desc()).offset(skip).limit(limit)
        refresh_result = await db.execute(refresh_stmt)
        tasks = refresh_result.scalars().all()
    else:
        refresh_stmt = select(Task).join(User, Task.creator_id == User.id).order_by(Task.created_at.desc()).offset(skip).limit(limit)
        refresh_result = await db.execute(refresh_stmt)
        tasks = refresh_result.scalars().all()

    # 为每个任务添加创建者用户名
    for task in tasks:
        creator_stmt = select(User).where(User.id == task.creator_id)
        creator_result = await db.execute(creator_stmt)
        creator = creator_result.scalar_one_or_none()
        if creator:
            task.creator_username = creator.username

    return {
        "items": tasks,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=TaskResponse)
async def create_task(
    task: TaskCreate,
    current_user: User = Depends(require_email_verification),
    db: AsyncSession = Depends(get_async_db)
):
    """创建新任务"""
    share_token = generate_share_token()
    expires_at = china_now() + timedelta(days=task.duration_days + 7)  # 任务期限 + 7天缓冲

    db_task = Task(
        title=task.title,
        description=task.description,
        task_type=task.task_type,
        duration_days=task.duration_days,
        share_token=share_token,
        creator_id=current_user.id,
        expires_at=expires_at
    )

    db.add(db_task)
    await db.commit()
    await db.refresh(db_task)

    return db_task


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取单个任务详情"""
    stmt = select(Task).where(
        Task.id == task_id,
        Task.creator_id == current_user.id
    )
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查并更新任务状态
    await check_and_update_task_status_async(task_id, db)

    # 重新查询以获取最新状态
    refresh_stmt = select(Task).where(
        Task.id == task_id,
        Task.creator_id == current_user.id
    )
    refresh_result = await db.execute(refresh_stmt)
    task = refresh_result.scalar_one_or_none()

    return task


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    task_update: TaskUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """更新任务（管理员可更新所有任务，普通用户只能更新自己的任务）"""
    # 构建查询条件
    stmt = select(Task).where(Task.id == task_id)

    # 如果不是管理员，只能更新自己创建的任务
    if current_user.role != "admin":
        stmt = stmt.where(Task.creator_id == current_user.id)

    result = await db.execute(stmt)
    task = result.scalar_one_or_none()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 更新字段
    update_data = task_update.model_dump(exclude_unset=True)
    old_status = task.status

    for field, value in update_data.items():
        setattr(task, field, value)

    # 如果任务状态发生变化，同步更新对应账号的状态
    if 'status' in update_data and update_data['status'] != old_status:
        from ..models import AccountSubmission
        new_status = update_data['status']

        # 只有当任务状态不是"待提交"时，才同步账号状态
        # 因为"待提交"状态时还没有账号记录
        if new_status != "待提交":
            update_stmt = select(AccountSubmission).where(AccountSubmission.task_id == task_id)
            update_result = await db.execute(update_stmt)
            accounts = update_result.scalars().all()
            for account in accounts:
                account.status = new_status

    await db.commit()
    await db.refresh(task)

    # 如果任务状态变为已完成或已取消，触发邮件通知
    if 'status' in update_data and update_data['status'] != old_status:
        try:
            if update_data['status'] == "已完成":
                await email_hooks.trigger("task_completed", db=db, task=task, user=task.creator)
            elif update_data['status'] == "已取消":
                await email_hooks.trigger("task_cancelled", db=db, task=task, user=task.creator)
        except Exception as e:
            # 邮件发送失败不影响任务更新
            print(f"发送任务状态变更邮件失败: {str(e)}")

    return task


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """删除任务（管理员可删除所有任务，普通用户只能删除自己的任务）"""
    # 构建查询条件
    stmt = select(Task).where(Task.id == task_id)

    # 如果不是管理员，只能删除自己创建的任务
    if current_user.role != "admin":
        stmt = stmt.where(Task.creator_id == current_user.id)

    result = await db.execute(stmt)
    task = result.scalar_one_or_none()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    await db.delete(task)
    await db.commit()

    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/share")
async def regenerate_share_token(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """重新生成分享链接（管理员可操作所有任务，普通用户只能操作自己的任务）"""
    # 构建查询条件
    stmt = select(Task).where(Task.id == task_id)

    # 如果不是管理员，只能操作自己创建的任务
    if current_user.role != "admin":
        stmt = stmt.where(Task.creator_id == current_user.id)

    result = await db.execute(stmt)
    task = result.scalar_one_or_none()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    task.share_token = generate_share_token()
    await db.commit()

    return {"share_token": task.share_token, "share_url": f"/share/{task.share_token}"}


@router.post("/admin/update-expired")
async def admin_update_expired_tasks(
    current_user: User = Depends(get_current_active_user)
):
    """管理员手动更新过期任务状态"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )

    updated_count = update_expired_tasks()
    return {
        "message": f"成功更新了 {updated_count} 个过期任务的状态",
        "updated_count": updated_count
    }
